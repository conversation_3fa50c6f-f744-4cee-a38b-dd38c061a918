# 量化工程配置文件
# 全局随机种子设置，确保结果可复现
random_seed: 42

# 数据源配置
qlib_init:
  provider_uri: ./qlib_data
  region: cn
  provider: LocalProvider

# 市场和基准指数
market: &market all
benchmark: &benchmark "513980"

# 回测配置
port_analysis_config: &port_analysis_config
  strategy:
    class: TopkDropoutStrategy
    module_path: qlib.contrib.strategy.signal_strategy
    kwargs:
      signal: pred
      topk: 1  # 由于只有一支股票，设置为1
      n_drop: 0  # 不需要dropout
  executor:
    class: SimulatorExecutor
    module_path: qlib.backtest.executor
    kwargs:
      time_per_step: 5min  # 设置时间步长为5分钟
      generate_portfolio_metrics: True
  # 回测时间范围配置
  backtest_config:
    start_time: "2025-04-17 09:30:00"
    end_time: "2025-05-13 15:00:00"  # 与test段的结束时间一致
  # 基准指数配置
  benchmark_config:
    benchmark: *benchmark
    freq: 5min
    # 禁用重采样
    resample_rule: null

# 模型配置
# LSTM模型配置
lstm_model: &lstm_model
  class: LSTM
  module_path: qlib.contrib.model.pytorch_lstm
  kwargs:
    d_feat: 5
    hidden_size: 256  # 增加隐藏层大小
    num_layers: 4    # 增加网络层数
    dropout: 0.2     # 减少dropout以减少过拟合
    n_epochs: 100    # 增加训练轮数
    lr: 5e-5        # 降低学习率以更稳定地收敛
    metric: loss
    early_stop: 10   # 增加早停轮数
    batch_size: 128  # 减小批量大小以提高泛化能力
    GPU: 0
    seed: 42        # 添加固定随机种子，确保结果可复现

# LightGBM模型配置
lgb_model: &lgb_model
  class: LGBModel
  module_path: qlib.contrib.model.gbdt
  kwargs:
    loss: mse
    colsample_bytree: 0.8879
    learning_rate: 0.05
    subsample: 0.8789
    lambda_l1: 20.0
    lambda_l2: 50.0
    max_depth: 8
    num_leaves: 160
    num_boost_round: 200
    early_stopping_rounds: 50

# 融合模型配置
fusion_model: &fusion_model
  class: ModelFusion
  module_path: model_fusion
  kwargs:
    models_config:
      lstm:
        <<: *lstm_model
      lgb:
        <<: *lgb_model
    fusion_model: lgb
    learning_rate: 0.03
    num_leaves: 100
    max_depth: 6
    n_estimators: 150

# Stacking模型配置
stacking_model: &stacking_model
  class: StackingModel
  module_path: stacking_model
  kwargs:
    base_models_config:
      lstm:
        <<: *lstm_model
      lgb:
        <<: *lgb_model
    meta_model_type: lgb
    learning_rate: 0.03
    num_leaves: 100
    max_depth: 6
    n_estimators: 150

# 在线交易配置
online_trading:
  server_uri: "ws://127.0.0.1:8765/ws/predict"
  instrument: "513980"
  initial_capital: 100000
  position_sizing: "full"  # 可选: "full", "half", "third", "quarter"
  min_trade_interval: 10  # 最小交易间隔（分钟）
  stop_loss_pct: 0.03  # 止损比例
  take_profit_pct: 0.08  # 止盈比例
  trailing_stop_pct: 0.025  # 跟踪止损比例
  signal_threshold: 4  # 信号确认阈值
  signal_confirmation_window: 6  # 信号确认窗口
  max_consecutive_loss: 3  # 最大连续亏损次数
  preheating_period: 20  # 预热期长度，解决信号不一致问题

# 在线服务器配置
online_server:
  host: "127.0.0.1"
  port: 8765
  model_update_interval: 86400  # 模型更新间隔（秒），默认为1天

# 任务配置
task:
  model: *lstm_model
  reweighter: null
  dataset:
    class: DatasetH
    module_path: qlib.data.dataset
    kwargs:
      handler:
        class: DataHandlerLP
        module_path: qlib.data.dataset.handler
        kwargs:
          start_time: 2024-03-15
          end_time: 2025-12-31
          instruments: all
          infer_processors:
            - class: ProcessInf
              module_path: qlib.data.dataset.processor
              kwargs: {}
            - class: ZScoreNorm
              module_path: qlib.data.dataset.processor
              kwargs:
                fit_start_time: 2024-03-15
                fit_end_time: 2024-12-31
            - class: Fillna
              module_path: qlib.data.dataset.processor
              kwargs: {}
          learn_processors:
            - class: DropnaLabel
              module_path: qlib.data.dataset.processor
              kwargs: {}
          data_loader:
            class: QlibDataLoader
            module_path: qlib.data.dataset.loader
            kwargs:
              config:
                feature:
                  - ["$close", "$open", "$high", "$low", "$volume"]
                  - ["close", "open", "high", "low", "volume"]
                label:
                  - ["$close / Ref($close, -1) - 1"]
                  - ["LABEL0"]
              freq: 5min
      segments:
        train: [2024-03-15, 2024-12-31]
        valid: [2025-01-01, 2025-01-31]
        test:  [2025-04-17, 2025-05-13]
        infer: [2025-04-10, 2025-05-31]
  record:
    - class: SignalRecord
      module_path: qlib.workflow.record_temp
      kwargs:
        model: <MODEL>
        dataset: <DATASET>
